<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F8F9FA"
    android:padding="20dp"
    tools:context=".ui.home.HomeFragment">

    <!-- Nombre de la sucursal simplificado -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_sucursal"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:cardBackgroundColor="#123456"
        app:cardCornerRadius="12dp"
        app:cardElevation="3dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="20dp"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/tv_nombre_sucursal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Sucursal"
                android:textColor="#FFFFFF"
                android:textSize="18sp"
                android:textStyle="bold"
                android:gravity="center" />
        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>
    <!-- Card de cotización con color Info -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_cotizacion"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        app:cardBackgroundColor="#17a2b8"
        app:cardCornerRadius="12dp"
        app:cardElevation="3dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/card_sucursal">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="24dp"
            android:gravity="center_vertical">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Cotización USD/PYG"
                    android:textColor="#FFFFFF"
                    android:textSize="14sp"
                    android:textStyle="normal"
                    android:alpha="0.9" />

                <TextView
                    android:id="@+id/tv_cotizacion"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Gs. 0.00"
                    android:textColor="#FFFFFF"
                    android:textSize="22sp"
                    android:textStyle="bold"
                    android:layout_marginTop="8dp" />
            </LinearLayout>

            <ImageView
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:src="@drawable/ic_attach_money"
                app:tint="#FFFFFF"
                android:layout_marginStart="16dp" />
        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>

    <!-- Tarjeta principal de ventas con color Success -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_main"
        android:layout_width="0dp"
        android:layout_height="180dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        app:cardBackgroundColor="#28a745"
        app:cardCornerRadius="12dp"
        app:cardElevation="3dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/card_cotizacion">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="24dp">

            <TextView
                android:id="@+id/tv_titulo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Ventas del día"
                android:textColor="#FFFFFF"
                android:textSize="14sp"
                android:textStyle="normal"
                android:alpha="0.9"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_fecha"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:textColor="#FFFFFF"
                android:textSize="16sp"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_titulo" />

            <TextView
                android:id="@+id/tv_total"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="32dp"
                android:text="0.00"
                android:textColor="#FFFFFF"
                android:textSize="32sp"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_fecha" />

            <TextView
                android:id="@+id/tv_label_total"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="Total acumulado"
                android:textColor="#FFFFFF"
                android:textSize="14sp"
                android:alpha="0.9"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_total" />

            <ImageView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_trending_up"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tint="#FFFFFF" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.google.android.material.card.MaterialCardView>

    <!-- Tarjeta de Ticket medio (arriba) con color Warning -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_ticket"
        android:layout_width="0dp"
        android:layout_height="120dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        app:cardBackgroundColor="#ffc107"
        app:cardCornerRadius="12dp"
        app:cardElevation="3dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/card_main">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="20dp">

            <TextView
                android:id="@+id/tv_label_ticket"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Ventas promedio"
                android:textColor="#212529"
                android:textSize="14sp"
                android:alpha="0.8"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_ticket_medio"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="0.00"
                android:textColor="#212529"
                android:textSize="28sp"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_label_ticket" />

            <ImageView
                android:id="@+id/ic_ticket"
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:src="@drawable/ic_average"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tint="#212529" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.google.android.material.card.MaterialCardView>

    <!-- Tarjeta de Transacciones (abajo) con color Primary -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_transacciones"
        android:layout_width="0dp"
        android:layout_height="120dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        app:cardBackgroundColor="#007bff"
        app:cardCornerRadius="12dp"
        app:cardElevation="3dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/card_ticket">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="20dp">

            <TextView
                android:id="@+id/tv_label_transacciones"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Cant. Ventas"
                android:textColor="#FFFFFF"
                android:textSize="14sp"
                android:alpha="0.9"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_transacciones"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="0"
                android:textColor="#FFFFFF"
                android:textSize="28sp"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_label_transacciones" />

            <ImageView
                android:id="@+id/ic_transacciones"
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:src="@drawable/ic_receipt"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tint="#FFFFFF" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.google.android.material.card.MaterialCardView>

    <!-- ProgressBar para carga -->
    <ProgressBar
        android:id="@+id/progressBar"
        style="?android:attr/progressBarStyleLarge"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:indeterminateTint="#007bff"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Texto de versión minimalista -->
    <TextView
        android:id="@+id/tv_version"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Versión 1.3"
        android:textColor="#6c757d"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginBottom="16dp"/>

</androidx.constraintlayout.widget.ConstraintLayout>