package com.salesmobile.ui.pay;

import androidx.lifecycle.ViewModelProvider;

import android.app.AlertDialog;
import android.content.Context;
import android.content.SharedPreferences;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;

import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import com.salesmobile.R;
import com.salesmobile.ui.inputproduct.SharedViewModel;
import com.salesmobile.utils.enviarEmail;
import com.salesmobile.utils.generadorFacturas;
import com.salesmobile.utils.generadorRecibos;
import com.salesmobile.utils.impresion;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;

public class payFragment extends Fragment {
    private Button buttonInicio;
    private Button buttonVolver;
    private Button btnImprimirFactura;
    private Button btnImprimirRecibo;
    private ProgressBar progressBarImprimir;
    private TextView textNroFactura;
    private PayViewModel mViewModel;
    private SharedViewModel sharedViewModel;
    private JSONObject facturaData;
    private JSONObject reciboData;
    private File pdfGuardado; // Para almacenar el PDF guardado
    private String pdfClienteGuardado; // Para almacenar el PDF del cliente
    private String pdfComercioGuardado; // Para almacenar el PDF del comercio



    public static payFragment newInstance() {
        return new payFragment();
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        // Primero inflamos la vista y la asignamos a una variable
        View view = inflater.inflate(R.layout.fragment_pay, container, false);
        buttonInicio = view.findViewById(R.id.home);
        buttonVolver = view.findViewById(R.id.otraVenta);
        btnImprimirFactura = view.findViewById(R.id.btnImprimirFactura);
        btnImprimirRecibo = view.findViewById(R.id.btnImprimirRecibo);
        progressBarImprimir = view.findViewById(R.id.progressBarImprimir);
        textNroFactura = view.findViewById(R.id.textNroFactura);

        // Obtener SharedViewModel
        sharedViewModel = new ViewModelProvider(requireActivity()).get(SharedViewModel.class);

        // Observar datos de factura
        sharedViewModel.getFacturaDataLiveData().observe(getViewLifecycleOwner(), facturaResponse -> {
            Log.d("PAY_FRAGMENT", "Factura data recibida: " + (facturaResponse != null ? facturaResponse.toString() : "null"));
            if (facturaResponse != null) {
                this.facturaData = facturaResponse;
                btnImprimirFactura.setVisibility(View.VISIBLE);

                // Primera llamada: obtener PDF para cachear (EMAIL: false, PDF: true)
                enviarEmail.enviarEmail(requireContext(), facturaResponse, false, true, new enviarEmail.EmailCallback() {
                    @Override
                    public void onSuccess(String response) {
                        // Procesar y cachear el PDF
                        generadorFacturas.generarFacturaDesdeRespuesta(requireContext(), response, pdfGuardado, new generadorFacturas.FacturaCallback() {
                            @Override
                            public void onSuccess(String pdfFilePath) {
                                pdfGuardado = new File(pdfFilePath);
                                Log.d("PAY_FRAGMENT", "PDF cacheado exitosamente: " + pdfFilePath);
                            }

                            @Override
                            public void onError(String errorMessage) {
                                Log.e("PAY_FRAGMENT", "Error al cachear PDF: " + errorMessage);
                            }
                        });
                    }

                    @Override
                    public void onError(String errorMessage) {
                        Log.e("PAY_FRAGMENT", "Error al obtener PDF para cache: " + errorMessage);
                    }
                });

                // Segunda llamada: enviar email (EMAIL: true, PDF: false)
                enviarEmail.enviarEmail(requireContext(), facturaResponse, true, false, null);

                try {
                    Log.d("PAY_FRAGMENT", "Verificando campo informacionExtra...");
                    if (facturaResponse.has("informacionExtra")) {
                        JSONObject informacionExtra = facturaResponse.getJSONObject("informacionExtra");
                        Log.d("PAY_FRAGMENT", "informacionExtra encontrado: " + informacionExtra.toString());

                        String prefijo = informacionExtra.optString("prefijo", "");
                        String numero = informacionExtra.optString("numero", "");

                        Log.d("PAY_FRAGMENT", "Prefijo: '" + prefijo + "', Numero: '" + numero + "'");

                        if (!prefijo.isEmpty() && !numero.isEmpty()) {
                            String nroFactura = "Nro de Factura " + prefijo + "-" + numero;
                            Log.d("PAY_FRAGMENT", "Estableciendo número de factura: " + nroFactura);
                            textNroFactura.setText(nroFactura);
                            textNroFactura.setVisibility(View.VISIBLE);
                        } else {
                            Log.w("PAY_FRAGMENT", "Prefijo o número están vacíos");
                        }
                    } else {
                        Log.w("PAY_FRAGMENT", "Campo 'informacionExtra' no encontrado en la respuesta");
                        Log.d("PAY_FRAGMENT", "Campos disponibles: " + facturaResponse.keys().toString());
                    }
                } catch (Exception e) {
                    Log.e("PayFragment", "Error al extraer número de factura: " + e.getMessage(), e);
                }
            } else {
                Log.w("PAY_FRAGMENT", "facturaResponse es null - no se recibieron datos de factura");
            }
        });

        // Observar datos de PDF
        sharedViewModel.getPdfDataLiveData().observe(getViewLifecycleOwner(), pdfResponse -> {
            if (pdfResponse != null) {
                try {
                    if (pdfResponse.getBoolean("pdfAvailable")) {
                        String pdfPath = pdfResponse.getString("pdfPath");
                        pdfGuardado = new File(pdfPath);
                        Log.d("PDF", "PDF Guardado");
                    }
                } catch (Exception e) {
                    Log.e("PDF", "Error al guardar el PDF: " + e.getMessage());
                }
            }
        });

  
        sharedViewModel.getReciboDataLiveData().observe(getViewLifecycleOwner(), reciboResponse -> {
            if (reciboResponse != null) {
                this.reciboData = reciboResponse;
                btnImprimirRecibo.setVisibility(View.VISIBLE);
            }
        });

        buttonInicio.setOnClickListener(v -> {
            // Limpiar datos de factura y recibo antes de navegar
            sharedViewModel.clearFacturaData();
            sharedViewModel.clearReciboData();
            sharedViewModel.clearPdfData();
            pdfClienteGuardado = null;
            pdfComercioGuardado = null;
            NavController navController = Navigation.findNavController(view);
            navController.navigate(R.id.action_nav_pay_to_home);
        });

        buttonVolver.setOnClickListener(v -> {
            // Limpiar datos de factura y recibo antes de navegar
            sharedViewModel.clearFacturaData();
            sharedViewModel.clearReciboData();
            sharedViewModel.clearPdfData();
            pdfClienteGuardado = null;
            pdfComercioGuardado = null;
            NavController navController = Navigation.findNavController(view);
            navController.navigate(R.id.action_nav_pay_to_carritoFragment);
        });

        btnImprimirFactura.setOnClickListener(v -> {
            progressBarImprimir.setVisibility(View.VISIBLE);
            btnImprimirFactura.setEnabled(false);

            // Si existe PDF en cache, usarlo
            if (pdfGuardado != null && pdfGuardado.exists()) {
                imprimirPDFGuardado();
                return;
            }

            // Si no hay cache, llamar a la API para obtener PDF (EMAIL: false, PDF: true)
            if (facturaData != null) {
                enviarEmail.enviarEmail(requireContext(), facturaData, false, true, new enviarEmail.EmailCallback() {
                    @Override
                    public void onSuccess(String response) {
                        imprimirFactura(response);
                    }

                    @Override
                    public void onError(String errorMessage) {
                        requireActivity().runOnUiThread(() -> {
                            progressBarImprimir.setVisibility(View.GONE);
                            btnImprimirFactura.setEnabled(true);
                        });
                    }
                });
            } else {
                // Si no hay facturaData, también intentar obtenerla de la API
                Log.d("PAY_FRAGMENT", "No hay datos de factura, intentando obtener de API");
                requireActivity().runOnUiThread(() -> {
                    progressBarImprimir.setVisibility(View.GONE);
                    btnImprimirFactura.setEnabled(true);
                });
            }
        });

        btnImprimirRecibo.setOnClickListener(v -> {
            progressBarImprimir.setVisibility(View.VISIBLE);
            btnImprimirRecibo.setEnabled(false);

            // Verificar si ya tenemos los PDFs guardados en cache
            if (pdfClienteGuardado != null && pdfComercioGuardado != null) {
                File pdfClienteFile = new File(pdfClienteGuardado);
                File pdfComercioFile = new File(pdfComercioGuardado);

                if (pdfClienteFile.exists() && pdfComercioFile.exists()) {
                    // Usar PDFs desde cache
                    requireActivity().runOnUiThread(() -> {
                        progressBarImprimir.setVisibility(View.GONE);
                        btnImprimirRecibo.setEnabled(true);
                        mostrarDialogoImprimirComercio(pdfComercioGuardado, pdfClienteGuardado);
                    });
                    return;
                }
            }

            if (reciboData != null) {
                callReciboAPI(reciboData);
            } else {
                // Si no hay reciboData, también intentar obtenerla de la API
                Log.d("PAY_FRAGMENT", "No hay datos de recibo, intentando obtener de API");
                requireActivity().runOnUiThread(() -> {
                    progressBarImprimir.setVisibility(View.GONE);
                    btnImprimirRecibo.setEnabled(true);
                });
            }
        });

        return view;
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        mViewModel = new ViewModelProvider(this).get(PayViewModel.class);
    }

    private void mostrarDialogoImprimirFactura() {
        new AlertDialog.Builder(requireContext())
                .setTitle("Factura Generada")
                .setMessage("¿Desea imprimir la factura?")
                .setPositiveButton("Imprimir", (dialog, which) -> {
                    if (pdfGuardado != null && pdfGuardado.exists()) {
                        imprimirPDFGuardado();
                    }
                })
                .setNegativeButton("Cancelar", (dialog, which) -> {
                    dialog.dismiss();
                })
                .setCancelable(false)
                .show();
    }



    private void imprimirFactura(String response) {
        generadorFacturas.generarFacturaDesdeRespuesta(requireContext(), response, pdfGuardado, new generadorFacturas.FacturaCallback() {
            @Override
            public void onSuccess(String pdfFilePath) {
                pdfGuardado = new File(pdfFilePath);
                Log.d("PRINT", "Archivo PDF disponible: " + pdfFilePath);

                requireActivity().runOnUiThread(() -> {
                    progressBarImprimir.setVisibility(View.GONE);
                    btnImprimirFactura.setEnabled(true);
                    mostrarDialogoImprimirFactura();
                });
            }

            @Override
            public void onError(String errorMessage) {
                Log.e("PRINT", "Error al generar factura: " + errorMessage);
                requireActivity().runOnUiThread(() -> {
                    progressBarImprimir.setVisibility(View.GONE);
                    btnImprimirFactura.setEnabled(true);
                });
            }
        });
    }

    private void callReciboAPI(JSONObject reciboData) {
        try {
            progressBarImprimir.setVisibility(View.VISIBLE);
            btnImprimirRecibo.setEnabled(false);

            JSONObject paymentData = reciboData.getJSONObject("paymentData");

            SharedPreferences sharedPreferences = requireContext().getSharedPreferences("ConfigDB", Context.MODE_PRIVATE);
            String puestoventa = sharedPreferences.getString("puntoventa","");

            generadorRecibos.generarRecibo(requireContext(), paymentData, puestoventa, new generadorRecibos.ReciboCallback() {
                @Override
                public void onSuccess(String pdfClientePath, String pdfComercioPath) {
                    // Guardar los paths en cache
                    pdfClienteGuardado = pdfClientePath;
                    pdfComercioGuardado = pdfComercioPath;

                    requireActivity().runOnUiThread(() -> {
                        progressBarImprimir.setVisibility(View.GONE);
                        btnImprimirRecibo.setEnabled(true);
                        mostrarDialogoImprimirComercio(pdfComercioPath, pdfClientePath);
                    });
                    Log.d("RECIBO_SUCCESS", "Recibos generados exitosamente");
                }

                @Override
                public void onError(String errorMessage) {
                    requireActivity().runOnUiThread(() -> {
                        progressBarImprimir.setVisibility(View.GONE);
                        btnImprimirRecibo.setEnabled(true);
                        Log.d("RECIBO_ERROR", "Error al generar recibos: " + errorMessage);
                    });
                }
            });


        } catch (JSONException e) {
            Log.e("RECIBO_API", "Error creating recibo request: " + e.getMessage());
            progressBarImprimir.setVisibility(View.GONE);
            btnImprimirRecibo.setEnabled(true);
        }
    }



    private void imprimirPDFGuardado() {
        if (pdfGuardado == null || !pdfGuardado.exists()) {
            Log.d("PRINT_SAVED", "PDF no disponible");
            return;
        }

        progressBarImprimir.setVisibility(View.VISIBLE);
        btnImprimirFactura.setEnabled(false);

        impresion.imprimirPDFDesdeArchivo(requireContext(), pdfGuardado.getAbsolutePath(), "Factura de compra", new impresion.PrintCallback() {
            @Override
            public void onSuccess() {
                progressBarImprimir.setVisibility(View.GONE);
                btnImprimirFactura.setEnabled(true);
                Log.d("PRINT_SAVED", "Factura compartida desde archivo guardado");
            }

            @Override
            public void onError(String errorMessage) {
                Log.e("PRINT_SAVED", "Error al compartir PDF guardado: " + errorMessage);
                progressBarImprimir.setVisibility(View.GONE);
                btnImprimirFactura.setEnabled(true);
            }
        });
    }

    private void mostrarDialogoImprimirComercio(String pdfComercioPath, String pdfClientePath) {
        new AlertDialog.Builder(requireContext())
                .setTitle("Imprimir Recibo")
                .setMessage("¿Imprimir copia comercio?")
                .setPositiveButton("OK", (dialog, which) -> {
                    imprimirReciboPDF(pdfComercioPath, "Recibo Comercio");
                    mostrarDialogoImprimirCliente(pdfClientePath);
                })
                .setNegativeButton("Cancelar", (dialog, which) -> {
                    mostrarDialogoImprimirCliente(pdfClientePath);
                })
                .setCancelable(false)
                .show();
    }

    private void mostrarDialogoImprimirCliente(String pdfClientePath) {
        new AlertDialog.Builder(requireContext())
                .setTitle("Imprimir Recibo")
                .setMessage("¿Imprimir copia cliente?")
                .setPositiveButton("OK", (dialog, which) -> {
                    imprimirReciboPDF(pdfClientePath, "Recibo Cliente");
                    mostrarDialogoFacturaDespuesDeRecibos();
                })
                .setNegativeButton("Cancelar", (dialog, which) -> {
                    mostrarDialogoFacturaDespuesDeRecibos();
                })
                .setCancelable(false)
                .show();
    }

    private void mostrarDialogoFacturaDespuesDeRecibos() {
        if (facturaData != null) {
            new AlertDialog.Builder(requireContext())
                    .setTitle("Factura Generada")
                    .setMessage("¿Desea imprimir la factura?")
                    .setPositiveButton("Imprimir", (dialog, which) -> {
                        if (facturaData != null) {
                            progressBarImprimir.setVisibility(View.VISIBLE);
                            btnImprimirFactura.setEnabled(false);

                            enviarEmail.enviarEmail(requireContext(), facturaData, true, false, new enviarEmail.EmailCallback() {
                                @Override
                                public void onSuccess(String response) {
                                    Log.d("API", "Respuesta: " + response);
                                    imprimirFactura(response);
                                }

                                @Override
                                public void onError(String errorMessage) {
                                    Log.e("API", "Error: " + errorMessage);
                                    requireActivity().runOnUiThread(() -> {
                                        progressBarImprimir.setVisibility(View.GONE);
                                        btnImprimirFactura.setEnabled(true);
                                    });
                                }
                            });
                        } else {
                            //Toast.makeText(getContext(), "Datos de factura no disponibles", Toast.LENGTH_SHORT).show();
                            Log.d("PAY_FRAGMENT", "Datos de factura no disponibles");
                        }
                    })
                    .setNegativeButton("Cancelar", (dialog, which) -> {
                        // No hacer nada, el botón quedará disponible
                    })
                    .setCancelable(false)
                    .show();
        }
    }

    private void imprimirReciboPDF(String pdfPath, String tipoRecibo) {
        progressBarImprimir.setVisibility(View.VISIBLE);
        btnImprimirRecibo.setEnabled(false);

        impresion.imprimirPDFDesdeArchivo(requireContext(), pdfPath, tipoRecibo, new impresion.PrintCallback() {
            @Override
            public void onSuccess() {
                progressBarImprimir.setVisibility(View.GONE);
                btnImprimirRecibo.setEnabled(true);
            }

            @Override
            public void onError(String errorMessage) {
                Log.e("PRINT_RECIBO", "Error al compartir " + tipoRecibo + ": " + errorMessage);
                progressBarImprimir.setVisibility(View.GONE);
                btnImprimirRecibo.setEnabled(true);
            }
        });
    }
}