package com.salesmobile.ui.login;

import android.content.Intent;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ProgressBar;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import com.salesmobile.AppDatabase;
import com.salesmobile.Configuracion;
import com.salesmobile.ConexionFragment;
import com.salesmobile.DatabaseHelper;
import com.salesmobile.MainActivity;
import com.salesmobile.R;
import com.salesmobile.SQLServerConnection;
import com.salesmobile.config.SharedPreferences;
import com.salesmobile.config.ParametrosConf;
import com.salesmobile.SucursalInfoManager;
import java.sql.SQLException;
import java.util.Objects;
import java.util.concurrent.Executors;


public class LoginActivity extends AppCompatActivity {

    private EditText etUsuarioSQL, etPasswordSQL;
    private Button btnLogin;
    private ProgressBar progressBar;
    private FrameLayout fragmentContainer;
    private View loginForm;
    private boolean configVisible = false;
    private CheckBox cbRecordar;
    private SharedPreferences sharedPrefs;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_login);

        // Inicialización de vistas
        etUsuarioSQL = findViewById(R.id.etUsuarioSQL);
        etPasswordSQL = findViewById(R.id.etPasswordSQL);
        btnLogin = findViewById(R.id.btnLogin);
        Button btnConfiguracion = findViewById(R.id.btnConfiguracion);
        progressBar = findViewById(R.id.progressBar);
        fragmentContainer = findViewById(R.id.fragment_container);
        loginForm = findViewById(R.id.login_form);
        cbRecordar = findViewById(R.id.cbRecordar);

        sharedPrefs = new SharedPreferences(this);
        SharedPreferences.UserCredentials credentials = sharedPrefs.getUserCredentials();

        if (credentials.isRecordar()) {
            etUsuarioSQL.setText(credentials.getUsuario());
            etPasswordSQL.setText(credentials.getPassword());
            cbRecordar.setChecked(true);
        }

        btnLogin.setOnClickListener(v -> {
            String usuarioSQL = etUsuarioSQL.getText().toString().trim();
            String passwordSQL = etPasswordSQL.getText().toString().trim();
            boolean recordarChecked = cbRecordar.isChecked();
            if (validarCampos(usuarioSQL, passwordSQL)) {
                sharedPrefs.saveUserCredentials(usuarioSQL, passwordSQL, recordarChecked);
                conectarSQLServer(usuarioSQL, passwordSQL);
            }
        });

        btnConfiguracion.setOnClickListener(v -> toggleConfiguracion());
    }


    public void toggleConfiguracion() {
        if (configVisible) {
            // Ocultar fragmento y mostrar formulario de login
            getSupportFragmentManager().beginTransaction()
                    .remove(Objects.requireNonNull(getSupportFragmentManager().findFragmentById(R.id.fragment_container)))
                    .commit();
            fragmentContainer.setVisibility(View.GONE);
            loginForm.setVisibility(View.VISIBLE);
        } else {
            // Mostrar fragmento y ocultar formulario de login
            getSupportFragmentManager().beginTransaction()
                    .replace(R.id.fragment_container, new ConexionFragment())
                    .commit();
            fragmentContainer.setVisibility(View.VISIBLE);
            loginForm.setVisibility(View.GONE);
        }
        configVisible = !configVisible;
    }

    private boolean validarCampos(String usuarioSQL, String passwordSQL) {
        boolean valid = true;

        if (usuarioSQL.isEmpty()) {
            etUsuarioSQL.setError("Ingrese usuario de SQL Server");
            valid = false;
        }

        if (passwordSQL.isEmpty()) {
            etPasswordSQL.setError("Ingrese contraseña de SQL Server");
            valid = false;
        }

        return valid;
    }


    private void conectarSQLServer(String usuarioSQL, String passwordSQL) {
        progressBar.setVisibility(View.VISIBLE);
        btnLogin.setEnabled(false);

        String servidor = sharedPrefs.getServidor();
        String basedatos = sharedPrefs.getBaseDatos();
        


        SQLServerConnection.getConnection(
                this,
                servidor,
                basedatos,
                usuarioSQL,
                passwordSQL,
                sharedPrefs.getConfigDbPrefs(),
                (connection, syncSuccess, syncMessage) -> runOnUiThread(() -> {
                    progressBar.setVisibility(View.GONE);
                    btnLogin.setEnabled(true);



                    if (syncSuccess) {
         
                        ParametrosConf.init(LoginActivity.this);
    
                        if (ParametrosConf.PUNTOVENTA == null || ParametrosConf.PUNTOVENTA.isEmpty()){
                            Toast.makeText(LoginActivity.this, "Error: No se configuró punto de venta", Toast.LENGTH_LONG).show();
                            return;
                        }
                        if (ParametrosConf.COD_EMPRESA == null || ParametrosConf.COD_EMPRESA.isEmpty()) {
                            Toast.makeText(LoginActivity.this, "Error: No se configuró código de empresa", Toast.LENGTH_LONG).show();
                            return;
                        }
                        if (ParametrosConf.COD_SUCURSAL == null || ParametrosConf.COD_SUCURSAL.isEmpty()) {
                            Toast.makeText(LoginActivity.this, "Error: No se configuró código de sucursal", Toast.LENGTH_LONG).show();
                            return;
                        }
                        consultarInfoSucursalAntesDeNavegar();

                    } else {
                        Toast.makeText(LoginActivity.this, "Error en sincronización: " + syncMessage, Toast.LENGTH_LONG).show();
                    }

                    if (connection != null) {
                        try {
                            connection.close();
                        } catch (SQLException e) {
                            e.printStackTrace();
                        }
                    }
                }));
    }

    private void consultarInfoSucursalAntesDeNavegar() {
        try {

            java.util.List<com.salesmobile.config.SharedPreferences.ConfiguracionSincronizada> configuraciones = sharedPrefs.getSyncedConfiguraciones();

            if (configuraciones.isEmpty()) {
                Log.w("LoginActivity", "No hay configuraciones sincronizadas disponibles");
                navegarAMainActivity();
                return;
            }

            String codemp = configuraciones.get(0).getCodempresa();
            String codsuc = configuraciones.get(0).getCodsucursal();

            if (codemp == null || codemp.trim().isEmpty() || codsuc == null || codsuc.trim().isEmpty()) {
                Log.w("LoginActivity", "Códigos de empresa o sucursal vacíos en configuración");
                navegarAMainActivity();
                return;
            }

            Log.d("LoginActivity", "Consultando información de sucursal antes de navegar...");

            SucursalInfoManager sucursalInfoManager = new SucursalInfoManager(this);
            sucursalInfoManager.consultarInfoSucursal(codemp, codsuc, new SucursalInfoManager.SucursalInfoCallback() {
                @Override
                public void onSuccess(SucursalInfoManager.SucursalInfo sucursalInfo) {
                    runOnUiThread(() -> {
                        Log.d("LoginActivity", "Información de sucursal obtenida exitosamente: " + sucursalInfo.getNombre());
                        navegarAMainActivity();
                    });
                }

                @Override
                public void onError(String errorMessage) {
                    runOnUiThread(() -> {
                        Log.e("LoginActivity", "Error al obtener información de sucursal: " + errorMessage);
                        navegarAMainActivity();
                    });
                }
            });

        } catch (Exception e) {
            Log.e("LoginActivity", "Error al consultar información de sucursal", e);
            navegarAMainActivity();
        }
    }

    private void navegarAMainActivity() {
        Intent intent = new Intent(LoginActivity.this, MainActivity.class);
        intent.putExtra("NAVIGATE_TO", "SELECT_CLIENT");
        startActivity(intent);
        finish();
    }

}
